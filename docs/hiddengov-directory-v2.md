# HiddenGov 项目目录结构 v2.0

```
hiddengov/
├── .github/                           # GitHub配置
│   └── workflows/
│       ├── ci.yml                    # 持续集成
│       └── deploy.yml                # 部署流程
├── .husky/                           # Git hooks
│   ├── pre-commit                    # 提交前检查
│   └── pre-push                      # 推送前检查
├── public/                           # 静态资源
│   ├── favicon.ico
│   ├── logo.svg                      # HiddenGov Logo
│   └── robots.txt
├── src/                              # 源代码目录
│   ├── app/                          # Next.js 15 App Router
│   │   ├── (auth)/                   # 认证路由组
│   │   │   ├── login/
│   │   │   │   ├── page.tsx          # 登录页面
│   │   │   │   └── loading.tsx       # 登录加载状态
│   │   │   ├── register/
│   │   │   │   ├── page.tsx          # 注册页面
│   │   │   │   └── loading.tsx
│   │   │   ├── layout.tsx            # 认证页面布局
│   │   │   └── error.tsx             # 认证错误处理
│   │   ├── (dashboard)/              # 主功能路由组
│   │   │   ├── page.tsx              # 仪表盘首页 (us_sam_gov_stats)
│   │   │   ├── projects/             # 项目机会模块
│   │   │   │   ├── page.tsx          # 项目列表 (us_sam_gov_project_view)
│   │   │   │   ├── [id]/             # 项目详情
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── loading.tsx       # 表格加载状态
│   │   │   │   └── error.tsx         # 查询错误处理
│   │   │   ├── contacts/             # 采购人信息模块
│   │   │   │   ├── page.tsx          # 采购人列表 (us_sam_gov_contact_view)
│   │   │   │   ├── [id]/
│   │   │   │   │   └── page.tsx      # 采购人详情
│   │   │   │   └── loading.tsx
│   │   │   ├── suppliers/            # 供应商信息模块
│   │   │   │   ├── page.tsx          # 供应商列表 (us_sam_gov_awardee_view)
│   │   │   │   ├── [id]/
│   │   │   │   │   └── page.tsx      # 供应商详情
│   │   │   │   └── loading.tsx
│   │   │   ├── archived/             # 历史数据模块
│   │   │   │   ├── page.tsx          # 历史数据查询
│   │   │   │   └── loading.tsx
│   │   │   ├── profile/              # 个人设置模块
│   │   │   │   ├── page.tsx          # 个人信息 (us_sam_u_profiles)
│   │   │   │   ├── usage/
│   │   │   │   │   └── page.tsx      # 使用记录
│   │   │   │   └── settings/
│   │   │   │       └── page.tsx      # 账户设置
│   │   │   ├── help/                 # 帮助文档
│   │   │   │   └── page.tsx
│   │   │   └── layout.tsx            # 仪表盘布局
│   │   ├── admin/                    # 管理后台路由组
│   │   │   ├── page.tsx              # 管理仪表盘
│   │   │   ├── users/                # 用户管理
│   │   │   │   ├── page.tsx          # 用户列表
│   │   │   │   ├── [id]/
│   │   │   │   │   ├── page.tsx      # 用户详情
│   │   │   │   │   └── edit/
│   │   │   │   │       └── page.tsx  # 编辑用户
│   │   │   │   └── create/
│   │   │   │       └── page.tsx      # 创建用户
│   │   │   ├── invite-codes/         # 邀请码管理
│   │   │   │   ├── page.tsx          # 邀请码列表
│   │   │   │   ├── create/
│   │   │   │   │   └── page.tsx      # 创建邀请码
│   │   │   │   └── batch-create/
│   │   │   │       └── page.tsx      # 批量创建
│   │   │   ├── logs/                 # 用户日志管理
│   │   │   │   ├── page.tsx          # 日志列表 (us_sam_u_logs)
│   │   │   │   └── [id]/
│   │   │   │       └── page.tsx      # 日志详情
│   │   │   ├── layout.tsx            # 管理后台布局
│   │   │   └── error.tsx             # 管理错误处理
│   │   ├── api/                      # API路由
│   │   │   ├── auth/                 # 认证相关API
│   │   │   │   ├── register/
│   │   │   │   │   └── route.ts      # POST 用户注册
│   │   │   │   ├── validate-invite/
│   │   │   │   │   └── route.ts      # POST 验证邀请码
│   │   │   │   └── turnstile/
│   │   │   │       └── route.ts      # POST Turnstile验证
│   │   │   ├── dashboard/            # 仪表盘API
│   │   │   │   └── stats/
│   │   │   │       └── route.ts      # GET 统计数据
│   │   │   ├── search/               # 统一搜索API
│   │   │   │   ├── projects/
│   │   │   │   │   └── route.ts      # GET/POST 项目搜索
│   │   │   │   ├── contacts/
│   │   │   │   │   └── route.ts      # GET/POST 采购人搜索
│   │   │   │   ├── suppliers/
│   │   │   │   │   └── route.ts      # GET/POST 供应商搜索
│   │   │   │   └── archived/
│   │   │   │       └── route.ts      # GET/POST 历史数据搜索
│   │   │   ├── export/               # 数据导出API
│   │   │   │   ├── csv/
│   │   │   │   │   └── route.ts      # POST CSV导出
│   │   │   │   └── status/
│   │   │   │       └── route.ts      # GET 导出状态
│   │   │   ├── users/                # 用户相关API
│   │   │   │   ├── profile/
│   │   │   │   │   └── route.ts      # GET/PUT 用户档案
│   │   │   │   ├── usage/
│   │   │   │   │   └── route.ts      # GET 使用记录
│   │   │   │   └── [id]/
│   │   │   │       └── route.ts      # GET/PUT/DELETE 用户操作
│   │   │   └── admin/                # 管理员API
│   │   │       ├── users/
│   │   │       │   └── route.ts      # GET/POST 用户管理
│   │   │       ├── invite-codes/
│   │   │       │   ├── route.ts      # GET/POST 邀请码管理
│   │   │       │   └── batch/
│   │   │       │       └── route.ts  # POST 批量创建邀请码
│   │   │       └── logs/
│   │   │           └── route.ts      # GET 日志查询
│   │   ├── globals.css               # 全局样式
│   │   ├── layout.tsx                # 根布局
│   │   ├── page.tsx                  # 首页/登录引导
│   │   ├── not-found.tsx             # 404页面
│   │   ├── error.tsx                 # 全局错误处理
│   │   └── loading.tsx               # 全局加载状态
│   ├── components/                   # 组件目录
│   │   ├── ui/                       # shadcn-ui基础组件
│   │   │   ├── button.tsx
│   │   │   ├── input.tsx
│   │   │   ├── table.tsx
│   │   │   ├── card.tsx
│   │   │   ├── dialog.tsx
│   │   │   ├── form.tsx
│   │   │   ├── select.tsx
│   │   │   ├── toast.tsx
│   │   │   ├── avatar.tsx            # 结合DiceBear
│   │   │   └── theme-toggle.tsx      # 明暗模式切换
│   │   ├── features/                 # 业务功能组件
│   │   │   ├── auth/                 # 认证相关组件
│   │   │   │   ├── login-form.tsx
│   │   │   │   ├── register-form.tsx
│   │   │   │   ├── invite-code-input.tsx
│   │   │   │   └── turnstile-widget.tsx
│   │   │   ├── dashboard/            # 仪表盘组件
│   │   │   │   ├── stats-overview.tsx
│   │   │   │   ├── recent-activity.tsx
│   │   │   │   ├── quick-search.tsx
│   │   │   │   └── trend-charts.tsx
│   │   │   ├── search/               # 搜索功能组件
│   │   │   │   ├── search-bar.tsx    # 全局搜索框
│   │   │   │   ├── advanced-filters.tsx
│   │   │   │   ├── quick-filters.tsx
│   │   │   │   ├── filter-tags.tsx
│   │   │   │   └── search-history.tsx
│   │   │   ├── data-table/           # 表格组件系列
│   │   │   │   ├── data-table.tsx    # 通用数据表格
│   │   │   │   ├── table-pagination.tsx
│   │   │   │   ├── table-sorting.tsx
│   │   │   │   ├── column-filter.tsx
│   │   │   │   ├── row-selection.tsx
│   │   │   │   └── export-button.tsx
│   │   │   ├── projects/             # 项目相关组件
│   │   │   │   ├── project-list.tsx
│   │   │   │   ├── project-detail.tsx
│   │   │   │   ├── project-filters.tsx
│   │   │   │   └── contract-timeline.tsx
│   │   │   ├── contacts/             # 采购人相关组件
│   │   │   │   ├── contact-list.tsx
│   │   │   │   ├── contact-detail.tsx
│   │   │   │   └── agency-filter.tsx
│   │   │   ├── suppliers/            # 供应商相关组件
│   │   │   │   ├── supplier-list.tsx
│   │   │   │   ├── supplier-detail.tsx
│   │   │   │   ├── supplier-analytics.tsx
│   │   │   │   └── competition-chart.tsx
│   │   │   ├── export/               # 导出功能组件
│   │   │   │   ├── export-dialog.tsx
│   │   │   │   ├── export-progress.tsx
│   │   │   │   ├── export-history.tsx
│   │   │   │   └── field-selector.tsx
│   │   │   ├── profile/              # 个人设置组件
│   │   │   │   ├── profile-form.tsx
│   │   │   │   ├── usage-stats.tsx
│   │   │   │   ├── subscription-info.tsx
│   │   │   │   └── avatar-selector.tsx
│   │   │   └── admin/                # 管理功能组件
│   │   │       ├── user-management/
│   │   │       │   ├── user-list.tsx
│   │   │       │   ├── user-form.tsx
│   │   │       │   └── user-stats.tsx
│   │   │       ├── invite-codes/
│   │   │       │   ├── code-list.tsx
│   │   │       │   ├── code-generator.tsx
│   │   │       │   └── batch-creator.tsx
│   │   │       └── logs/
│   │   │           ├── log-viewer.tsx
│   │   │           ├── log-filters.tsx
│   │   │           └── activity-chart.tsx
│   │   └── layout/                   # 布局组件
│   │       ├── header.tsx            # 顶部导航
│   │       ├── sidebar.tsx           # 侧边栏导航
│   │       ├── footer.tsx            # 页脚
│   │       ├── breadcrumb.tsx        # 面包屑导航
│   │       └── mobile-nav.tsx        # 移动端导航
│   ├── hooks/                        # 自定义Hooks
│   │   ├── use-auth.ts               # 认证状态管理
│   │   ├── use-search.ts             # 搜索状态管理
│   │   ├── use-data-table.ts         # 表格状态管理
│   │   ├── use-debounce.ts           # 防抖Hook
│   │   ├── use-local-storage.ts      # 本地存储Hook
│   │   ├── use-pagination.ts         # 分页Hook
│   │   ├── use-filters.ts            # 筛选Hook
│   │   └── use-export.ts             # 导出功能Hook
│   ├── lib/                          # 工具库
│   │   ├── supabase/                 # Supabase相关
│   │   │   ├── client.ts             # 客户端实例
│   │   │   ├── server.ts             # 服务端实例
│   │   │   ├── admin.ts              # 管理端实例
│   │   │   └── types.ts              # 数据库类型定义
│   │   ├── validations/              # Zod验证模式
│   │   │   ├── auth.ts               # 认证表单验证
│   │   │   ├── search.ts             # 搜索参数验证
│   │   │   ├── user.ts               # 用户数据验证
│   │   │   ├── invite-code.ts        # 邀请码验证
│   │   │   └── export.ts             # 导出参数验证
│   │   ├── utils/                    # 工具函数
│   │   │   ├── format.ts             # 数据格式化
│   │   │   ├── date.ts               # 日期处理
│   │   │   ├── currency.ts           # 金额格式化
│   │   │   ├── export-csv.ts         # CSV导出工具
│   │   │   ├── search-utils.ts       # 搜索工具
│   │   │   ├── table-utils.ts        # 表格工具
│   │   │   └── constants.ts          # 常量定义
│   │   ├── api/                      # API工具
│   │   │   ├── client.ts             # API客户端
│   │   │   ├── endpoints.ts          # 端点定义
│   │   │   ├── types.ts              # API类型
│   │   │   └── error-handler.ts      # 错误处理
│   │   └── auth/                     # 认证工具
│   │       ├── session.ts            # 会话管理
│   │       ├── permissions.ts        # 权限检查
│   │       └── turnstile.ts          # Turnstile集成
│   ├── stores/                       # 状态管理
│   │   ├── auth-store.ts             # 认证状态
│   │   ├── search-store.ts           # 搜索状态
│   │   ├── ui-store.ts               # UI状态
│   │   └── preferences-store.ts      # 用户偏好
│   ├── types/                        # TypeScript类型
│   │   ├── database.ts               # 数据库类型
│   │   ├── api.ts                    # API类型
│   │   ├── auth.ts                   # 认证类型
│   │   ├── search.ts                 # 搜索类型
│   │   ├── table.ts                  # 表格类型
│   │   └── index.ts                  # 类型导出
│   └── middleware.ts                 # Next.js中间件
├── tests/                            # 测试文件
│   ├── __mocks__/                    # Mock文件
│   ├── components/                   # 组件测试
│   ├── hooks/                        # Hook测试
│   ├── utils/                        # 工具函数测试
│   ├── api/                          # API测试
│   ├── e2e/                          # 端到端测试
│   ├── setup.ts                      # 测试配置
│   └── jest.config.js                # Jest配置
├── docs/                             # 项目文档
│   ├── api.md                        # API文档
│   ├── deployment.md                 # 部署文档
│   └── development.md                # 开发文档
├── .env.example                      # 环境变量示例
├── .env.local                        # 本地环境变量
├── .eslintrc.json                    # ESLint配置
├── .gitignore                        # Git忽略规则
├── .prettierrc                       # Prettier配置
├── components.json                   # shadcn-ui配置
├── next-env.d.ts                     # Next.js类型
├── next.config.js                    # Next.js配置
├── package.json                      # 项目依赖
├── pnpm-lock.yaml                    # 依赖锁定
├── postcss.config.js                 # PostCSS配置
├── README.md                         # 项目说明
├── tailwind.config.ts                # Tailwind配置
└── tsconfig.json                     # TypeScript配置
```

## 核心目录说明

### 页面路由组织 (`src/app/`)
- **(dashboard)/** - 主功能区，包含所有核心业务模块
- **admin/** - 管理后台，独立的管理功能区
- **(auth)/** - 认证相关页面，独立的认证流程

### 功能模块对应 (`src/components/features/`)
- **dashboard/** - 仪表盘统计和概览组件
- **search/** - 多维度搜索功能组件  
- **data-table/** - 表格优先的数据展示组件
- **projects/contacts/suppliers/** - 对应数据视图的专用组件
- **admin/** - 管理功能的专用组件

### 数据层架构 (`src/lib/`)
- **supabase/** - 数据库客户端和类型定义
- **validations/** - 所有表单和API的Zod验证
- **utils/** - 业务相关的工具函数
- **api/** - API客户端和错误处理

### 开发特性
- **类型安全** - 完整的TypeScript类型定义覆盖
- **组件复用** - shadcn-ui + 自定义业务组件的分层架构
- **性能优化** - 基于Next.js 15 App Router的SSR优先架构
- **测试覆盖** - 完整的单元测试和E2E测试结构

### 部署配置
- **环境变量** - 分离的公开和私密配置管理
- **构建优化** - Tailwind CSS + shadcn-ui的最优化配置
- **安全设置** - 中间件权限控制和安全头配置

这个目录结构充分体现了HiddenGov作为投资研究数据分析平台的特点，突出了多维度查询、表格优先展示、和管理功能的完整实现。