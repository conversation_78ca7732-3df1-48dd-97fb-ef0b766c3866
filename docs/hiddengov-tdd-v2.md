# HiddenGov 技术设计文档 (TDD) v3.0

## 1. 系统架构设计

### 1.1 整体架构图
```
┌─────────────────────────────────────────┐
│              用户界面层                  │
│   Next.js 15 App Router + 200行限制     │
├─────────────────────────────────────────┤
│              业务逻辑层                  │
│   单一职责模块 | 强制文件拆分 | 权限控制  │
├─────────────────────────────────────────┤
│              数据访问层                  │
│     7天试用期控制 + RLS权限策略         │
├─────────────────────────────────────────┤
│              数据存储层                  │
│  PostgreSQL + 分区表 + 20年历史数据     │
└─────────────────────────────────────────┘
```

### 1.2 核心技术原则
- **单一职责原则**：每个文件只负责一个具体功能
- **200行强制限制**：超过200行必须拆分为多个文件
- **7天试用期控制**：基于时间的权限验证机制
- **模块化设计**：功能模块的独立性和可维护性

## 2. 权限控制架构

### 2.1 7天试用期实现

#### 试用期验证中间件
```typescript
// middleware/trial-period-check.ts (< 200行)
export async function checkTrialPeriod(userId: string): Promise<boolean> {
  const profile = await getUserProfile(userId)
  
  if (!profile) return false
  
  const now = new Date()
  const trialEnd = new Date(profile.created_at)
  trialEnd.setDate(trialEnd.getDate() + 7)
  
  return now <= trialEnd
}
```

#### RLS策略实现
```sql
-- 7天试用期数据访问策略
CREATE POLICY "trial_period_access" ON data_views
FOR SELECT USING (
  auth.uid() IN (
    SELECT auth_user_id FROM us_sam_u_profiles 
    WHERE is_active = true 
    AND (
      created_at >= NOW() - INTERVAL '7 days' OR
      subscription_end > NOW()
    )
  )
);
```

### 2.2 角色权限分层

#### 用户权限矩阵
```typescript
// types/permissions.ts (< 200行)
interface PermissionMatrix {
  USER: {
    dashboard: true
    projects: true
    contacts: true
    suppliers: true
    archived: true
    profile: true
    help: true
    admin: false
  }
  ADMIN: {
    // 继承USER权限
    admin: true
    userManagement: true
    inviteManagement: true
    logViewing: true
  }
  SUPER: {
    // 继承ADMIN权限
    systemConfig: true
    roleManagement: true
  }
}
```

## 3. 文件组织架构

### 3.1 200行限制实施策略

#### 组件拆分示例
```typescript
// ❌ 错误：单个文件过大 (>200行)
// components/data-table.tsx (350行)

// ✅ 正确：拆分为多个专职文件
// components/data-table/table-header.tsx    (< 200行)
// components/data-table/table-body.tsx      (< 200行)
// components/data-table/table-pagination.tsx (< 200行)
// components/data-table/table-filters.tsx   (< 200行)
// components/data-table/index.ts            (导出文件)
```

#### API路由拆分策略
```typescript
// ❌ 错误：复杂API路由 (>200行)
// app/api/search/route.ts

// ✅ 正确：按功能拆分
// app/api/search/projects/route.ts          (< 200行)
// app/api/search/contacts/route.ts          (< 200行)
// app/api/search/suppliers/route.ts         (< 200行)
// lib/api-handlers/search-base.ts           (< 200行)
// lib/api-handlers/search-validators.ts     (< 200行)
```

### 3.2 强制拆分检查机制

#### 构建时检查
```typescript
// scripts/check-file-size.ts (< 200行)
import { readFileSync, readdirSync } from 'fs'
import { join } from 'path'

export function checkFileSizeLimit(dir: string, limit: number = 200) {
  const violations: string[] = []
  
  function scanDirectory(currentDir: string) {
    const items = readdirSync(currentDir, { withFileTypes: true })
    
    for (const item of items) {
      if (item.isFile() && item.name.endsWith('.ts', '.tsx')) {
        const filePath = join(currentDir, item.name)
        const content = readFileSync(filePath, 'utf-8')
        const lineCount = content.split('\n').length
        
        if (lineCount > limit) {
          violations.push(`${filePath}: ${lineCount} lines`)
        }
      } else if (item.isDirectory()) {
        scanDirectory(join(currentDir, item.name))
      }
    }
  }
  
  scanDirectory(dir)
  return violations
}
```

## 4. 数据访问层设计

### 4.1 视图访问控制

#### 统一数据访问接口
```typescript
// lib/database/data-access.ts (< 200行)
interface DataAccessConfig {
  viewName: string
  userRole: UserRole
  trialPeriodValid: boolean
}

export async function accessDataView(
  config: DataAccessConfig,
  filters: FilterOptions
) {
  // 权限验证
  if (!config.trialPeriodValid) {
    throw new Error('Trial period expired')
  }
  
  // 数据查询逻辑
  const query = buildQuery(config.viewName, filters)
  return executeQuery(query)
}
```

#### 查询构建器拆分
```typescript
// lib/database/query-builder/base.ts        (< 200行)
// lib/database/query-builder/filters.ts     (< 200行)
// lib/database/query-builder/pagination.ts  (< 200行)
// lib/database/query-builder/sorting.ts     (< 200行)
```

### 4.2 历史数据访问优化

#### 分区查询策略
```typescript
// lib/database/partition-query.ts (< 200行)
export class PartitionQueryBuilder {
  constructor(private tableName: string) {}
  
  // 年份范围查询优化
  byYearRange(startYear: number, endYear: number) {
    const partitions = this.getPartitionsForYears(startYear, endYear)
    return this.buildUnionQuery(partitions)
  }
  
  // 动态分区选择
  private getPartitionsForYears(start: number, end: number): string[] {
    const partitions: string[] = []
    for (let year = start; year <= end; year++) {
      partitions.push(`${this.tableName}_${year}`)
    }
    return partitions
  }
}
```

## 5. 前端架构设计

### 5.1 组件文件拆分规范

#### 页面组件拆分
```typescript
// app/(dashboard)/projects/page.tsx (< 200行)
// 只负责：数据获取 + 组件组装

// components/features/projects/project-list.tsx      (< 200行)
// components/features/projects/project-filters.tsx   (< 200行)
// components/features/projects/project-search.tsx    (< 200行)
// components/features/projects/project-export.tsx    (< 200行)
```

#### 状态管理拆分
```typescript
// stores/auth-store.ts          (< 200行) - 认证状态
// stores/search-store.ts        (< 200行) - 搜索状态
// stores/ui-store.ts            (< 200行) - UI状态
// stores/preferences-store.ts   (< 200行) - 用户偏好
```

### 5.2 Hooks拆分策略

#### 功能专用Hooks
```typescript
// hooks/use-trial-period.ts     (< 200行) - 试用期检查
// hooks/use-search-debounce.ts  (< 200行) - 搜索防抖
// hooks/use-data-export.ts      (< 200行) - 数据导出
// hooks/use-table-state.ts      (< 200行) - 表格状态
// hooks/use-permissions.ts      (< 200行) - 权限检查
```

## 6. API设计架构

### 6.1 路由文件拆分

#### 按功能模块拆分API
```typescript
// 每个API路由文件 < 200行
app/api/
├── auth/
│   ├── register/route.ts      (< 200行)
│   ├── trial-check/route.ts   (< 200行)
│   └── validate-invite/route.ts (< 200行)
├── dashboard/
│   └── stats/route.ts         (< 200行)
├── search/
│   ├── projects/route.ts      (< 200行)
│   ├── contacts/route.ts      (< 200行)
│   ├── suppliers/route.ts     (< 200行)
│   └── archived/route.ts      (< 200行)
└── admin/
    ├── users/route.ts         (< 200行)
    ├── invites/route.ts       (< 200行)
    └── logs/route.ts          (< 200行)
```

### 6.2 共享逻辑抽取

#### API处理器抽象
```typescript
// lib/api-handlers/base-handler.ts (< 200行)
export abstract class BaseApiHandler {
  protected async validateTrialPeriod(userId: string): Promise<void> {
    const isValid = await checkTrialPeriod(userId)
    if (!isValid) {
      throw new ApiError('Trial period expired', 'TRIAL_EXPIRED', 403)
    }
  }
  
  protected async validatePermissions(
    userId: string, 
    requiredPermission: string
  ): Promise<void> {
    const hasPermission = await checkUserPermission(userId, requiredPermission)
    if (!hasPermission) {
      throw new ApiError('Insufficient permissions', 'NO_PERMISSION', 403)
    }
  }
}
```

## 7. 验证层架构

### 7.1 Zod Schema拆分

#### 按功能域拆分验证
```typescript
// lib/validations/auth.ts           (< 200行)
// lib/validations/search-projects.ts (< 200行)
// lib/validations/search-contacts.ts (< 200行)
// lib/validations/search-suppliers.ts (< 200行)
// lib/validations/user-profile.ts   (< 200行)
// lib/validations/admin-operations.ts (< 200行)
```

#### 验证器组合模式
```typescript
// lib/validations/common.ts (< 200行)
export const baseSearchSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(10).max(100).default(20),
  keyword: z.string().max(100).optional()
})

// lib/validations/search-projects.ts (< 200行)
export const projectSearchSchema = baseSearchSchema.extend({
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  amountMin: z.coerce.number().min(0).optional(),
  agencies: z.array(z.string()).optional()
})
```

## 8. 工具函数架构

### 8.1 功能专用工具函数

#### 按职责拆分工具函数
```typescript
// lib/utils/date-formatter.ts      (< 200行) - 日期格式化
// lib/utils/currency-formatter.ts  (< 200行) - 金额格式化
// lib/utils/csv-exporter.ts        (< 200行) - CSV导出
// lib/utils/search-highlighter.ts  (< 200行) - 搜索高亮
// lib/utils/trial-calculator.ts    (< 200行) - 试用期计算
```

#### 数据处理工具拆分
```typescript
// lib/data-processors/project-processor.ts   (< 200行)
// lib/data-processors/contact-processor.ts   (< 200行)
// lib/data-processors/supplier-processor.ts  (< 200行)
// lib/data-processors/archive-processor.ts   (< 200行)
```

## 9. 测试架构设计

### 9.1 测试文件拆分

#### 按组件功能拆分测试
```typescript
// tests/components/auth/login-form.test.ts       (< 200行)
// tests/components/search/search-bar.test.ts     (< 200行)
// tests/components/table/data-table.test.ts      (< 200行)
// tests/hooks/use-trial-period.test.ts           (< 200行)
// tests/utils/trial-calculator.test.ts           (< 200行)
```

### 9.2 测试工具拆分

#### 测试辅助函数
```typescript
// tests/helpers/auth-helpers.ts     (< 200行) - 认证测试辅助
// tests/helpers/data-helpers.ts     (< 200行) - 数据测试辅助
// tests/helpers/ui-helpers.ts       (< 200行) - UI测试辅助
// tests/mocks/supabase-mock.ts      (< 200行) - Supabase模拟
```

## 10. 性能优化架构

### 10.1 缓存层拆分

#### 专用缓存处理器
```typescript
// lib/cache/trial-period-cache.ts   (< 200行) - 试用期缓存
// lib/cache/search-results-cache.ts (< 200行) - 搜索结果缓存
// lib/cache/user-profile-cache.ts   (< 200行) - 用户档案缓存
// lib/cache/stats-cache.ts          (< 200行) - 统计数据缓存
```

### 10.2 优化策略实现

#### 查询优化处理器
```typescript
// lib/optimizers/query-optimizer.ts     (< 200行)
// lib/optimizers/table-optimizer.ts     (< 200行)
// lib/optimizers/export-optimizer.ts    (< 200行)
```

## 11. 监控与日志架构

### 11.1 日志记录拆分

#### 专用日志记录器
```typescript
// lib/loggers/auth-logger.ts        (< 200行) - 认证日志
// lib/loggers/search-logger.ts      (< 200行) - 搜索日志
// lib/loggers/export-logger.ts      (< 200行) - 导出日志
// lib/loggers/error-logger.ts       (< 200行) - 错误日志
```

### 11.2 监控指标收集

#### 指标收集器拆分
```typescript
// lib/metrics/performance-metrics.ts (< 200行) - 性能指标
// lib/metrics/usage-metrics.ts       (< 200行) - 使用指标
// lib/metrics/trial-metrics.ts       (< 200行) - 试用期指标
```

## 12. 部署架构设计

### 12.1 环境配置拆分

#### 配置文件专职化
```typescript
// config/database.ts        (< 200行) - 数据库配置
// config/auth.ts             (< 200行) - 认证配置
// config/cache.ts            (< 200行) - 缓存配置
// config/monitoring.ts       (< 200行) - 监控配置
```

### 12.2 构建脚本拆分

#### 专用构建脚本
```typescript
// scripts/build-check.ts         (< 200行) - 构建检查
// scripts/file-size-check.ts     (< 200行) - 文件大小检查
// scripts/type-check.ts          (< 200行) - 类型检查
// scripts/lint-check.ts          (< 200行) - 代码规范检查
```

---

**版本**：v3.0  
**更新时间**：2024-12-19  
**核心原则**：单一职责 + 200行限制 + 7天试用期