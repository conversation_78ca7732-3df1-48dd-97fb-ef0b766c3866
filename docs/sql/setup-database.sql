-- HiddenGov 数据库设置脚本
-- 在 Supabase SQL 编辑器中执行此脚本

-- 1. 创建用户档案表
CREATE TABLE IF NOT EXISTS us_sam_u_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
    email TEXT NOT NULL,
    full_name TEXT NOT NULL,
    role TEXT DEFAULT 'USER' CHECK (role IN ('USER', 'ADMIN', 'SUPER')),
    is_active BOOLEAN DEFAULT true,
    subscription_start TIMESTAMPTZ,
    subscription_end TIMESTAMPTZ,
    invited_by UUID REFERENCES auth.users(id),
    invite_code_used TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. 创建邀请码表
CREATE TABLE IF NOT EXISTS us_sam_u_invite_codes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    code TEXT UNIQUE NOT NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    max_uses INTEGER DEFAULT 1,
    current_uses INTEGER DEFAULT 0,
    expires_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. 创建日志表
CREATE TABLE IF NOT EXISTS us_sam_u_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    event_type TEXT NOT NULL,
    event_data JSONB DEFAULT '{}',
    ip_address TEXT,
    user_agent TEXT,
    event_time TIMESTAMPTZ DEFAULT NOW()
);

-- 4. 创建项目视图（模拟数据）
CREATE OR REPLACE VIEW us_sam_gov_project_view AS
SELECT 
    gen_random_uuid() as id,
    'SOL-' || LPAD((ROW_NUMBER() OVER())::TEXT, 8, '0') as solicitationnumber,
    'Sample Government Project ' || (ROW_NUMBER() OVER()) as title,
    CASE 
        WHEN (ROW_NUMBER() OVER()) % 3 = 0 THEN 'CONTRACT'
        WHEN (ROW_NUMBER() OVER()) % 3 = 1 THEN 'GRANT'
        ELSE 'OTHER'
    END as type,
    'Department of ' || 
    CASE 
        WHEN (ROW_NUMBER() OVER()) % 4 = 0 THEN 'Defense'
        WHEN (ROW_NUMBER() OVER()) % 4 = 1 THEN 'Health and Human Services'
        WHEN (ROW_NUMBER() OVER()) % 4 = 2 THEN 'Transportation'
        ELSE 'Education'
    END as fullparentpathname,
    (NOW() - INTERVAL '1 day' * (ROW_NUMBER() OVER()))::DATE::TEXT as posted_date,
    (NOW() + INTERVAL '30 days' * (ROW_NUMBER() OVER()))::DATE::TEXT as response_deadline,
    (NOW() + INTERVAL '365 days' * (ROW_NUMBER() OVER()))::DATE::TEXT as archive_date,
    'John Doe ' || (ROW_NUMBER() OVER()) as poc_fullname,
    CASE WHEN (ROW_NUMBER() OVER()) % 2 = 0 THEN 'Acme Corp' ELSE NULL END as awardee_name,
    (ROW_NUMBER() OVER()) % 2 = 0 as has_resources,
    TO_CHAR((NOW() - INTERVAL '1 day' * (ROW_NUMBER() OVER())), 'YYYY-MM-DD') as formatted_posted_date
FROM generate_series(1, 50) as t(n);

-- 5. 创建 RLS 策略
ALTER TABLE us_sam_u_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE us_sam_u_invite_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE us_sam_u_logs ENABLE ROW LEVEL SECURITY;

-- 用户档案策略
CREATE POLICY "Users can view own profile" ON us_sam_u_profiles
    FOR SELECT USING (auth.uid() = auth_user_id);

CREATE POLICY "Users can update own profile" ON us_sam_u_profiles
    FOR UPDATE USING (auth.uid() = auth_user_id);

-- 邀请码策略（只有管理员可以管理）
CREATE POLICY "Admins can manage invite codes" ON us_sam_u_invite_codes
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM us_sam_u_profiles 
            WHERE auth_user_id = auth.uid() 
            AND role IN ('ADMIN', 'SUPER')
        )
    );

-- 日志策略（只有管理员可以查看）
CREATE POLICY "Admins can view logs" ON us_sam_u_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM us_sam_u_profiles 
            WHERE auth_user_id = auth.uid() 
            AND role IN ('ADMIN', 'SUPER')
        )
    );

-- 6. 创建测试邀请码
INSERT INTO us_sam_u_invite_codes (code, created_by, max_uses, current_uses, expires_at, is_active)
VALUES 
    ('TESTCODE123', NULL, 10, 0, NOW() + INTERVAL '30 days', true),
    ('DEMO2024', NULL, 5, 0, NOW() + INTERVAL '7 days', true)
ON CONFLICT (code) DO NOTHING;

-- 7. 创建索引
CREATE INDEX IF NOT EXISTS idx_profiles_auth_user_id ON us_sam_u_profiles(auth_user_id);
CREATE INDEX IF NOT EXISTS idx_profiles_email ON us_sam_u_profiles(email);
CREATE INDEX IF NOT EXISTS idx_invite_codes_code ON us_sam_u_invite_codes(code);
CREATE INDEX IF NOT EXISTS idx_logs_auth_user_id ON us_sam_u_logs(auth_user_id);
CREATE INDEX IF NOT EXISTS idx_logs_event_type ON us_sam_u_logs(event_type);

-- 完成提示
SELECT 'Database setup completed successfully!' as status;
